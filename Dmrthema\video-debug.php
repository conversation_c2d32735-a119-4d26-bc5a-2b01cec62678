<?php
/**
 * Video Eklentisi Debug Sayfasi
 * 
 * Bu dosya video eklentisinin durumunu kontrol etmek icin kullanilir.
 * Kullanim: /wp-content/themes/Dmrthema/video-debug.php?product_id=123
 */

// WordPress'i yukle
require_once('../../../wp-load.php');

// Sadece admin kullanicilari erisebilsin
if (!current_user_can('manage_options')) {
    wp_die('Bu sayfaya erismek icin yetkiniz yok.');
}

$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Video Eklentisi Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-form { margin: 20px 0; }
        .test-form input, .test-form button { padding: 8px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Video Eklentisi Debug Bilgileri</h1>
    
    <div class="test-form">
        <form method="get">
            <label>Urun ID:</label>
            <input type="number" name="product_id" value="<?php echo $product_id; ?>" placeholder="Urun ID girin">
            <button type="submit">Test Et</button>
        </form>
    </div>

    <div class="debug-section <?php echo class_exists('WooCustom_Featured_Video') ? 'success' : 'error'; ?>">
        <h3>1. Video Eklentisi Durumu</h3>
        <p><strong>WooCustom_Featured_Video sinifi:</strong> 
        <?php echo class_exists('WooCustom_Featured_Video') ? 'AKTIF ✓' : 'BULUNAMADI ✗'; ?></p>
        
        <?php if (class_exists('WooCustom_Featured_Video')): ?>
            <p><strong>Eklenti instance:</strong> 
            <?php 
            $instance = WooCustom_Featured_Video::instance();
            echo $instance ? 'BASLATILDI ✓' : 'BASLATILMADI ✗';
            ?>
            </p>
        <?php endif; ?>
    </div>

    <div class="debug-section info">
        <h3>2. Hook Durumu</h3>
        <?php
        global $wp_filter;
        $hook_name = 'woocommerce_single_product_image_thumbnail_html';
        
        if (isset($wp_filter[$hook_name])) {
            echo "<p><strong>$hook_name hook:</strong> MEVCUT ✓</p>";
            echo "<p><strong>Callback sayisi:</strong> " . count($wp_filter[$hook_name]->callbacks) . "</p>";
            
            foreach ($wp_filter[$hook_name]->callbacks as $priority => $callbacks) {
                echo "<p><strong>Oncelik $priority:</strong></p>";
                echo "<ul>";
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function'])) {
                        $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                        $method = $callback['function'][1];
                        echo "<li>$class::$method</li>";
                    } else {
                        echo "<li>" . $callback['function'] . "</li>";
                    }
                }
                echo "</ul>";
            }
        } else {
            echo "<p><strong>$hook_name hook:</strong> BULUNAMADI ✗</p>";
        }
        ?>
    </div>

    <?php if ($product_id > 0): ?>
        <div class="debug-section info">
            <h3>3. Urun Bilgileri (ID: <?php echo $product_id; ?>)</h3>
            <?php
            $product = wc_get_product($product_id);
            if ($product) {
                echo "<p><strong>Urun adi:</strong> " . $product->get_name() . "</p>";
                echo "<p><strong>Urun tipi:</strong> " . $product->get_type() . "</p>";
                
                $video_url = $product->get_meta('_woo_custom_video_url');
                $aspect_ratio = $product->get_meta('_woo_custom_video_aspect_ratio');
                
                echo "<p><strong>Video URL:</strong> " . ($video_url ? $video_url . ' ✓' : 'YOK ✗') . "</p>";
                echo "<p><strong>Video aspect ratio:</strong> " . ($aspect_ratio ? $aspect_ratio : '16:9 (varsayilan)') . "</p>";
                
                if ($video_url) {
                    // Video embed URL'sini test et
                    $embed_url = '';
                    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $video_url, $matches)) {
                        $embed_url = 'https://www.youtube.com/embed/' . $matches[1] . '?rel=0&showinfo=0&enablejsapi=1';
                        echo "<p><strong>Video tipi:</strong> YouTube ✓</p>";
                    } elseif (preg_match('/vimeo\.com\/(\d+)/', $video_url, $matches)) {
                        $embed_url = 'https://player.vimeo.com/video/' . $matches[1];
                        echo "<p><strong>Video tipi:</strong> Vimeo ✓</p>";
                    } else {
                        echo "<p><strong>Video tipi:</strong> DESTEKLENMEYEN ✗</p>";
                    }
                    
                    if ($embed_url) {
                        echo "<p><strong>Embed URL:</strong> <a href='$embed_url' target='_blank'>$embed_url</a></p>";
                    }
                }
            } else {
                echo "<p class='error'>Urun bulunamadi!</p>";
            }
            ?>
        </div>
    <?php endif; ?>

    <div class="debug-section info">
        <h3>4. CSS/JS Dosya Durumu</h3>
        <?php
        // CSS dosyalarini kontrol et
        $css_files = array(
            'woo-custom-featured-video-frontend' => WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-frontend.css',
            'dmrthema-product' => get_template_directory_uri() . '/assets/css/product.css',
            'dmrthema-video-compat' => get_template_directory_uri() . '/assets/css/video-compat.css'
        );
        
        $js_files = array(
            'woo-custom-featured-video-frontend' => WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-frontend.js',
            'dmrthema-product-js' => get_template_directory_uri() . '/assets/js/product.js'
        );
        
        echo "<h4>CSS Dosyalari:</h4>";
        foreach ($css_files as $handle => $url) {
            $exists = file_exists(str_replace(array(WOO_CUSTOM_PLUGIN_URL, get_template_directory_uri()), array(WOO_CUSTOM_PLUGIN_DIR, get_template_directory()), $url));
            echo "<p><strong>$handle:</strong> " . ($exists ? "MEVCUT ✓" : "BULUNAMADI ✗") . " - <a href='$url' target='_blank'>$url</a></p>";
        }
        
        echo "<h4>JavaScript Dosyalari:</h4>";
        foreach ($js_files as $handle => $url) {
            $exists = file_exists(str_replace(array(WOO_CUSTOM_PLUGIN_URL, get_template_directory_uri()), array(WOO_CUSTOM_PLUGIN_DIR, get_template_directory()), $url));
            echo "<p><strong>$handle:</strong> " . ($exists ? "MEVCUT ✓" : "BULUNAMADI ✗") . " - <a href='$url' target='_blank'>$url</a></p>";
        }
        ?>
    </div>

    <div class="debug-section info">
        <h3>5. WordPress ve WooCommerce Bilgileri</h3>
        <p><strong>WordPress versiyonu:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>WooCommerce versiyonu:</strong> <?php echo defined('WC_VERSION') ? WC_VERSION : 'BULUNAMADI'; ?></p>
        <p><strong>Aktif tema:</strong> <?php echo get_template(); ?></p>
        <p><strong>PHP versiyonu:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>WP_DEBUG:</strong> <?php echo defined('WP_DEBUG') && WP_DEBUG ? 'AKTIF' : 'KAPALI'; ?></p>
    </div>

    <div class="debug-section warning">
        <h3>6. Oneriler</h3>
        <ul>
            <li>Video eklentisinin aktif oldugundan emin olun</li>
            <li>Urun sayfasinda video URL'si eklendiginden emin olun</li>
            <li>Tarayici gelistirici araclari ile CSS/JS hatalarini kontrol edin</li>
            <li>WP_DEBUG'i aktif ederek hata loglarini inceleyin</li>
            <li>Tema ve eklenti dosyalarinin mevcut oldugundan emin olun</li>
        </ul>
    </div>

    <p><small>Debug sayfasi: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
