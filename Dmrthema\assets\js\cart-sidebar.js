/**
 * Sepet Sidebar JavaScript
 */

// <PERSON><PERSON> yuklendiginde calistir
window.addEventListener('load', function() {
    // Elementleri bul
    const cartToggle = document.getElementById('cart-toggle');
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartSidebarClose = document.getElementById('cart-sidebar-close');
    const cartSidebarOverlay = document.getElementById('cart-sidebar-overlay');

    // Sepet sidebar'ini ac
    function openCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            cartSidebar.classList.add('active');
            cartSidebarOverlay.classList.add('active');
            document.body.classList.add('cart-sidebar-open');
            document.documentElement.classList.add('cart-sidebar-open');
        }
    }

    // Sepet sidebar'ini kapat
    function closeCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            cartSidebar.classList.remove('active');
            cartSidebarOverlay.classList.remove('active');
            document.body.classList.remove('cart-sidebar-open');
            document.documentElement.classList.remove('cart-sidebar-open');
        }
    }



    // Cart toggle event listener'ini ekle
    function attachCartToggleListener() {
        const cartToggle = document.getElementById('cart-toggle');
        if (cartToggle) {
            // Onceki event listener'lari temizle
            cartToggle.removeEventListener('click', cartToggleHandler);
            // Yeni event listener ekle
            cartToggle.addEventListener('click', cartToggleHandler);
        }
    }

    // Cart toggle handler fonksiyonu
    function cartToggleHandler(e) {
        e.preventDefault();
        e.stopPropagation();
        openCartSidebar();
    }

    // Event listener'lar ekle
    if (cartToggle) {
        cartToggle.addEventListener('click', cartToggleHandler);
    }

    if (cartSidebarClose) {
        cartSidebarClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    if (cartSidebarOverlay) {
        cartSidebarOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    // ESC tusuna basildiginda sidebar'i kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && cartSidebar && cartSidebar.classList.contains('active')) {
            closeCartSidebar();
        }
    });

    // WooCommerce sepet guncelleme olaylarini dinle
    if (typeof jQuery !== 'undefined') {
        jQuery(document.body).on('added_to_cart removed_from_cart updated_wc_div wc_fragments_refreshed', function() {
            // Mini cart icerigini guncelle
            updateMiniCart();
            // Cart toggle event listener'ini yeniden ekle
            setTimeout(function() {
                attachCartToggleListener();
            }, 100);
        });

        // WooCommerce fragment refresh olayini dinle
        jQuery(document.body).on('wc_fragments_loaded wc_fragments_refreshed', function() {
            // Remove butonlarina yeniden event listener ekle
            setTimeout(function() {
                attachRemoveListeners();
                attachCartToggleListener();
            }, 100);
        });
    }

    // Sepet sayisini guncelle
    function updateCartCount() {
        if (typeof jQuery === 'undefined') return;

        jQuery.post(dmrthema_ajax.ajax_url, {
            action: 'get_cart_count'
        }, function(response) {
            if (response.success) {
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = response.data.count;
                }
            }
        });
    }

    // Mini cart icerigini guncelle
    function updateMiniCart() {
        if (typeof jQuery === 'undefined') return;

        jQuery.post(dmrthema_ajax.ajax_url, {
            action: 'get_mini_cart'
        }, function(response) {
            if (response.success) {
                const miniCartContent = document.querySelector('.widget_shopping_cart_content');
                if (miniCartContent) {
                    miniCartContent.innerHTML = response.data.mini_cart;
                    // Yeni eklenen remove butonlarina event listener ekle
                    attachRemoveListeners();
                }
            }
        });
    }

    // Remove butonlarina event listener ekle
    function attachRemoveListeners() {
        const removeButtons = document.querySelectorAll('.widget_shopping_cart_content .remove_from_cart_button');
        removeButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const cartItemKey = this.getAttribute('data-cart_item_key');
                const productId = this.getAttribute('data-product_id');

                if (typeof jQuery !== 'undefined') {
                    jQuery.post(dmrthema_ajax.ajax_url, {
                        action: 'remove_cart_item',
                        cart_item_key: cartItemKey,
                        product_id: productId,
                        nonce: dmrthema_ajax.nonce
                    }, function(response) {
                        if (response.success) {
                            updateCartCount();
                            updateMiniCart();
                            // WooCommerce event'ini tetikle
                            jQuery(document.body).trigger('removed_from_cart');
                        }
                    });
                }
            });
        });
    }

    // Sayfa yuklendiginde mevcut remove butonlarina listener ekle
    attachRemoveListeners();
});


