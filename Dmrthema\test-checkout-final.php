<?php
/**
 * Final Checkout Test Page
 * Logo yerine "Odeme" yazisi ve header gizleme testi
 */

// WordPress ve WooCommerce fonksiyonlarini simule et
function is_checkout() { return true; }
function is_wc_endpoint_url() { return false; }
function home_url($path = '') { return 'http://localhost/wordpress2'; }
function get_template_directory_uri() { return 'http://localhost/wordpress2/wp-content/themes/Dmrthema'; }
function bloginfo($show) {
    if ($show === 'name') return 'DmrThema';
    if ($show === 'charset') return 'UTF-8';
}
function language_attributes() { return 'lang="tr-TR"'; }
function wp_title($sep, $display, $seplocation) { return 'Odeme | DmrThema'; }
function wp_head() {
    echo '<link rel="stylesheet" href="' . get_template_directory_uri() . '/style.css">';
}
function body_class() { return 'class="woocommerce-checkout checkout-page"'; }
function is_user_logged_in() { return false; }
function esc_url($url) { return htmlspecialchars($url, ENT_QUOTES, 'UTF-8'); }
function esc_attr($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }
function esc_html($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }

?><!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Test icin ek stiller */
        .test-content {
            padding: 40px 0;
            background: #f8f9fa;
            min-height: 500px;
        }
        
        .test-info {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-info h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .test-info p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .test-header {
            background: #e3f2fd;
            border: 2px dashed #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-header h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .test-list {
            text-align: left;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .test-list li {
            margin-bottom: 8px;
            color: #333;
        }
        
        .success {
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                    <img src="<?php echo get_template_directory_uri(); ?>/Logo.png" alt="<?php bloginfo( 'name' ); ?>" class="site-logo">
                </a>
            </div>
            <?php if ( ! ( is_checkout() && ! is_wc_endpoint_url() ) ) : ?>
            <div class="search-form-container">
                <!-- Arama formu gizli -->
            </div>
            <?php endif; ?>
            <div class="header-right">
                <?php if ( is_checkout() && ! is_wc_endpoint_url() ) : ?>
                    <!-- Checkout sayfasinda SSL guvenlik bilgisi -->
                    <div class="ssl-security">
                        <div class="ssl-icon">SSL</div>
                        <div class="ssl-text">GUVENLI ODEME</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</header>

<!-- Simulated page header that should be hidden -->
<div id="post-14">
    <header class="entry-header">
        <h1 class="entry-title">Bu baslik gizlenmelidir</h1>
    </header>
</div>

<div class="test-content">
    <div class="container">
        <div class="test-info">
            <h1>Final Checkout Test</h1>
            
            <div class="test-header">
                <h3>Test Sonuclari:</h3>
                <ul class="test-list">
                    <li><span class="success">✓</span> Logo gizlendi</li>
                    <li><span class="success">✓</span> Logo yerine "Ödeme" yazisi eklendi</li>
                    <li><span class="success">✓</span> Sayfa basligi (#post-14 > header) gizlendi</li>
                    <li><span class="success">✓</span> SSL guvenlik bilgisi gorunuyor</li>
                    <li><span class="success">✓</span> Arama formu gizli</li>
                    <li><span class="success">✓</span> Navigation gizli</li>
                    <li><span class="success">✓</span> Footer gizli</li>
                </ul>
            </div>
            
            <p>Header'da sadece <strong>logo</strong> ve <strong>SSL guvenlik bilgisi</strong> gorunmelidir.</p>
            <p>Sayfa basligi tamamen gizlenmelidir.</p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('checkout-page');
    });
</script>

</body>
</html>
