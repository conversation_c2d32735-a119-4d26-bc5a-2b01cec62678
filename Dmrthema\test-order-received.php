<?php
/**
 * Order Received Test Page
 * Odeme tamamlandi sayfasi testi
 */

// WordPress ve WooCommerce fonksiyonlarini simule et
function is_checkout() { return true; }
function is_wc_endpoint_url($endpoint = '') { 
    if ($endpoint === 'order-received') return true;
    return false; 
}
function home_url($path = '') { return 'http://localhost/wordpress2'; }
function get_template_directory_uri() { return 'http://localhost/wordpress2/wp-content/themes/Dmrthema'; }
function bloginfo($show) {
    if ($show === 'name') return 'DmrThema';
    if ($show === 'charset') return 'UTF-8';
}
function language_attributes() { return 'lang="tr-TR"'; }
function wp_title($sep, $display, $seplocation) { return 'Odeme Tamamlandi | DmrThema'; }
function wp_head() {
    echo '<link rel="stylesheet" href="' . get_template_directory_uri() . '/style.css">';
}
function body_class() { return 'class="woocommerce-checkout woocommerce-order-received checkout-page"'; }
function is_user_logged_in() { return false; }
function esc_url($url) { return htmlspecialchars($url, ENT_QUOTES, 'UTF-8'); }
function esc_attr($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }
function esc_html($text) { return htmlspecialchars($text, ENT_QUOTES, 'UTF-8'); }
function wp_footer() { 
    echo '<script src="' . get_template_directory_uri() . '/js/main.js"></script>';
}

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Test sayfasi icin ek stiller */
        .test-info {
            background: #f0f8ff;
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: Arial, sans-serif;
        }
        .test-info h2 {
            color: #007cba;
            margin-top: 0;
        }
        .test-info p {
            margin: 10px 0;
            line-height: 1.6;
        }
        .test-info strong {
            color: #d63384;
        }
        

    </style>
</head>

<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                    <img src="<?php echo get_template_directory_uri(); ?>/Logo.png" alt="<?php bloginfo( 'name' ); ?>" class="site-logo">
                </a>
            </div>
            <?php if ( ! ( ( is_checkout() && ! is_wc_endpoint_url() ) || ( is_checkout() && is_wc_endpoint_url('order-received') ) ) ) : ?>
            <div class="search-form-container">
                <!-- Arama formu gizli -->
            </div>
            <?php endif; ?>
            <div class="header-right">
                <?php if ( ( is_checkout() && ! is_wc_endpoint_url() ) || ( is_checkout() && is_wc_endpoint_url('order-received') ) ) : ?>
                    <!-- Checkout ve order received sayfasinda SSL guvenlik bilgisi -->
                    <div class="ssl-security">
                        <div class="ssl-icon">SSL</div>
                        <div class="ssl-text">GUVENLI ODEME</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</header>

<!-- Simulated page header that should be hidden -->
<div id="post-14">
    <header class="entry-header">
        <h1 class="entry-title">Bu baslik gizlenmelidir</h1>
    </header>
</div>

<div class="container">
    <div class="test-info">
        <h2>🎉 Ödeme Tamamlandı Sayfası Test</h2>
        <div class="test-results">
            <p><strong>Test Edilen Özellikler:</strong></p>
            <ul>
                <li>✅ Header'da <strong>logo</strong> görünür</li>
                <li>✅ SSL güvenlik bilgisi görünür</li>
                <li>✅ Arama formu gizli</li>
                <li>✅ Kullanıcı menüsü gizli</li>
                <li>✅ Ana menü gizli</li>
                <li>✅ Sepet butonu gizli</li>
                <li>✅ Footer tamamen gizli</li>
                <li>✅ Sayfa başlığı gizli</li>
            </ul>
            
            <p><strong>Beklenen Görünüm:</strong></p>
            <p>Header'da sadece <strong>logo</strong> ve <strong>SSL güvenlik bilgisi</strong> görünmelidir.</p>
            <p>Sayfa başlığı tamamen gizlenmelidir.</p>
            <p>Footer görünmemelidir.</p>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-top: 15px;">
                <strong>📝 Not:</strong> Bu sayfa ödeme tamamlandı (order-received) sayfasını simüle eder.
                <br>Gerçek WooCommerce ortamında bu sayfa sipariş tamamlandıktan sonra otomatik olarak gösterilir.
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('checkout-page');
        document.body.classList.add('woocommerce-order-received');
    });
</script>

</body>
</html>
