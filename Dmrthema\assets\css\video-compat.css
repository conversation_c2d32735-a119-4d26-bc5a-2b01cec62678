/**
 * Video Eklentisi Uyumluluk CSS'i
 * WooCustom Featured Video eklentisi ile Dmrthema uyumlulugu
 * 
 * @package Dmrthema
 * @since 1.0.0
 */

/* Video gallery entegrasyonu */
.woo-custom-featured-video-gallery {
    position: relative;
    width: 100% !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
}

.woo-custom-featured-video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    position: relative;
    background: transparent;
}

.woo-custom-featured-video .video-container {
    position: relative;
    width: 100%;
    height: 0;
    overflow: hidden;
    border-radius: 10px;
    background: #000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Aspect ratio ayarlari */
.woo-custom-featured-video.aspect-16-9 .video-container {
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.woo-custom-featured-video.aspect-4-3 .video-container {
    padding-bottom: 75%; /* 4:3 aspect ratio */
}

.woo-custom-featured-video .video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 10px;
}

/* FlexSlider ile uyumluluk */
.flex-viewport .woo-custom-featured-video {
    width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0 !important;
}

.flex-viewport .woo-custom-featured-video .video-container {
    border-radius: 10px;
}

/* Gallery thumbnail'lari icin video gostergesi */
.woocommerce-product-gallery .flex-control-thumbs li img[src*="youtube"],
.woocommerce-product-gallery .flex-control-thumbs li img[src*="vimeo"],
.woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="youtube"],
.woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="vimeo"] {
    position: relative;
}

.woocommerce-product-gallery .flex-control-thumbs li img[src*="youtube"]:after,
.woocommerce-product-gallery .flex-control-thumbs li img[src*="vimeo"]:after,
.woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="youtube"]:after,
.woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="vimeo"]:after {
    content: "\f04b"; /* Play icon */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    z-index: 2;
    text-shadow: 0 0 3px rgba(0,0,0,0.8);
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Lightbox'ta video gosterme */
.pswp .woo-custom-featured-video,
.photoswipe .woo-custom-featured-video {
    display: none !important;
}

/* Video loading durumu */
.woo-custom-featured-video.loading .video-container {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: loading-animation 1s linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woo-custom-featured-video.loading .video-container:before {
    content: "Video yukleniyor...";
    color: #666;
    font-size: 14px;
    background: rgba(255,255,255,0.9);
    padding: 10px 15px;
    border-radius: 5px;
    position: relative;
    z-index: 2;
}

@keyframes loading-animation {
    0% {
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    }
    100% {
        background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
    }
}

/* Video error durumu */
.woo-custom-featured-video.error .video-container {
    background: #f8f8f8;
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.woo-custom-featured-video.error .video-container:before {
    content: "Video yuklenemedi";
    color: #999;
    font-size: 14px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .woo-custom-featured-video .video-container {
        border-radius: 8px;
    }
    
    .woo-custom-featured-video .video-container iframe {
        border-radius: 8px;
    }
    
    .woocommerce-product-gallery .flex-control-thumbs li img[src*="youtube"]:after,
    .woocommerce-product-gallery .flex-control-thumbs li img[src*="vimeo"]:after,
    .woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="youtube"]:after,
    .woocommerce-product-gallery .flex-control-thumbs li[data-thumb*="vimeo"]:after {
        font-size: 8px;
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 480px) {
    .woo-custom-featured-video .video-container {
        border-radius: 6px;
    }
    
    .woo-custom-featured-video .video-container iframe {
        border-radius: 6px;
    }
}

/* Tema spesifik ayarlar */
.single-product .woocommerce-product-gallery .woo-custom-featured-video {
    margin: 0 !important;
    padding: 0 !important;
}

.woocommerce-product-gallery__wrapper .woo-custom-featured-video-gallery {
    width: 100% !important;
    display: block !important;
}

/* Gallery navigation ile uyumluluk */
.woocommerce-product-gallery .flex-direction-nav {
    z-index: 10;
}

.woocommerce-product-gallery .flex-control-thumbs {
    z-index: 5;
}

/* Video container hover efekti */
.woo-custom-featured-video .video-container:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* Video iframe'in pointer events'ini etkinlestir */
.woo-custom-featured-video .video-container iframe {
    pointer-events: auto;
}

/* Gallery trigger button'u video uzerinde gosterme */
.woo-custom-featured-video-gallery .woocommerce-product-gallery__trigger {
    display: none !important;
}
